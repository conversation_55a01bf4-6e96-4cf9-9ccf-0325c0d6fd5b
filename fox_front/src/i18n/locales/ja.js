export default {
  // 共通
  common: {
    confirm: '確認',
    cancel: 'キャンセル',
    save: '保存',
    delete: '削除',
    edit: '編集',
    add: '追加',
    search: '検索',
    reset: 'リセット',
    submit: '送信',
    back: '戻る',
    next: '次へ',
    previous: '前へ',
    loading: '読み込み中...',
    success: '操作が成功しました',
    error: '操作が失敗しました',
    warning: '警告',
    info: '情報',
    close: '閉じる',
    refresh: '更新',
    export: 'エクスポート',
    import: 'インポート',
    copy: 'コピー',
    paste: '貼り付け',
    cut: '切り取り',
    selectAll: 'すべて選択',
    clear: 'クリア',
    upload: 'アップロード',
    download: 'ダウンロード',
    preview: 'プレビュー',
    print: '印刷',
    settings: '設定',
    help: 'ヘルプ',
    about: 'について',
    version: 'バージョン',
    language: '言語',
    theme: 'テーマ',
    darkMode: 'ダークモード',
    lightMode: 'ライトモード'
  },

  // ナビゲーションメニュー
  menu: {
    chatgpt: 'ChatGPT',
    claude: 'Claude',
    grok: 'Grok',
    drawing: '4O描画',
    purchase: '購入',
    useNote: '使用説明',
    announcement: 'お知らせ',
    exchange: '交換',
    userCenter: 'ユーザーセンター',
    myWorks: 'マイワーク',
    backend: '管理パネル',
    logout: 'ログアウト'
  },

  // ユーザー関連
  user: {
    login: 'ログイン',
    register: '登録',
    logout: 'ログアウト',
    username: 'ユーザー名',
    password: 'パスワード',
    confirmPassword: 'パスワード確認',
    email: 'メール',
    phone: '電話番号',
    captcha: '認証コード',
    rememberMe: 'ログイン状態を保持',
    forgotPassword: 'パスワードを忘れた',
    resetPassword: 'パスワードリセット',
    changePassword: 'パスワード変更',
    oldPassword: '旧パスワード',
    newPassword: '新パスワード',
    profile: 'プロフィール',
    avatar: 'アバター',
    nickname: 'ニックネーム',
    gender: '性別',
    birthday: '誕生日',
    address: '住所',
    bio: '自己紹介',
    loginSuccess: 'ログイン成功',
    loginFailed: 'ログイン失敗',
    registerSuccess: '登録成功',
    registerFailed: '登録失敗',
    logoutSuccess: 'ログアウト成功',
    passwordChanged: 'パスワード変更成功',
    profileUpdated: 'プロフィール更新成功',
    emailVerification: 'メール認証',
    sendVerificationCode: '認証コード送信',
    verificationCodeSent: '認証コードを送信しました',
    invalidCredentials: 'ユーザー名またはパスワードが間違っています',
    accountLocked: 'アカウントがロックされています',
    accountExpired: 'アカウントが期限切れです',
    permissionDenied: '権限がありません'
  },

  // システム設定
  config: {
    systemName: 'システム名',
    systemLogo: 'システムロゴ',
    siteNotice: 'サイト通知',
    emailConfig: 'メール設定',
    smtpHost: 'SMTPホスト',
    smtpPort: 'SMTPポート',
    senderEmail: '送信者メール',
    emailPassword: 'メールパスワード',
    emailWhiteList: 'メールホワイトリスト',
    canRegister: '登録許可',
    registerGift: '登録特典',
    systemMaintenance: 'システムメンテナンス',
    configUpdated: '設定更新成功',
    configFailed: '設定更新失敗'
  },

  // 車管理
  car: {
    carList: '車リスト',
    carName: '車名',
    carStatus: '車ステータス',
    carType: '車タイプ',
    carLimit: '使用制限',
    carExpire: '有効期限',
    addCar: '車追加',
    editCar: '車編集',
    deleteCar: '車削除',
    carNotFound: '車が見つかりません',
    carAccessDenied: '車へのアクセスが拒否されました',
    carLimitExceeded: '車の使用回数が上限に達しました',
    carAdded: '車追加成功',
    carUpdated: '車更新成功',
    carDeleted: '車削除成功'
  },

  // 描画機能
  drawing: {
    generateImage: '画像生成',
    imagePrompt: '画像説明',
    imageSize: '画像サイズ',
    imageCount: '生成数',
    imageStyle: '画像スタイル',
    generating: '生成中...',
    generateSuccess: '画像生成成功',
    generateFailed: '画像生成失敗',
    downloadImage: '画像ダウンロード',
    saveImage: '画像保存',
    shareImage: '画像共有',
    deleteImage: '画像削除',
    imageHistory: '生成履歴',
    noImages: '画像がありません',
    limitExceeded: '描画回数が上限に達しました'
  },

  // 支払い関連
  payment: {
    purchase: '購入',
    price: '価格',
    discount: '割引',
    total: '合計',
    paymentMethod: '支払い方法',
    alipay: 'Alipay',
    wechat: 'WeChat Pay',
    bankCard: '銀行カード',
    paymentSuccess: '支払い成功',
    paymentFailed: '支払い失敗',
    paymentPending: '支払い処理中',
    refund: '返金',
    refundSuccess: '返金成功',
    refundFailed: '返金失敗',
    orderNumber: '注文番号',
    orderStatus: '注文ステータス',
    orderHistory: '注文履歴'
  },

  // アクティベーションコード
  activation: {
    activationCode: 'アクティベーションコード',
    exchange: '交換',
    exchangeSuccess: '交換成功',
    exchangeFailed: '交換失敗',
    invalidCode: '無効なアクティベーションコード',
    usedCode: 'アクティベーションコードは既に使用されています',
    expiredCode: 'アクティベーションコードが期限切れです',
    codeHistory: '交換履歴',
    generateCode: 'アクティベーションコード生成',
    batchGenerate: '一括生成',
    codeType: 'コードタイプ',
    codeValue: 'コード価値',
    codeExpire: '有効期限'
  },

  // ファイルアップロード
  file: {
    upload: 'ファイルアップロード',
    uploadSuccess: 'アップロード成功',
    uploadFailed: 'アップロード失敗',
    fileSize: 'ファイルサイズ',
    fileType: 'ファイルタイプ',
    fileName: 'ファイル名',
    fileNotFound: 'ファイルが見つかりません',
    fileTooLarge: 'ファイルが大きすぎます',
    invalidFileType: 'ファイルタイプがサポートされていません',
    selectFile: 'ファイル選択',
    dragToUpload: 'ファイルをここにドラッグしてアップロード',
    uploadProgress: 'アップロード進行状況'
  },

  // フォーム検証
  validation: {
    required: 'この項目は必須です',
    email: '有効なメールアドレスを入力してください',
    phone: '有効な電話番号を入力してください',
    password: 'パスワードは最低6文字必要です',
    confirmPassword: 'パスワードが一致しません',
    minLength: '最低{min}文字入力してください',
    maxLength: '最大{max}文字まで入力可能です',
    numeric: '数字を入力してください',
    url: '有効なURLを入力してください',
    date: '有効な日付を入力してください',
    time: '有効な時刻を入力してください'
  },

  // ページネーション
  pagination: {
    total: '合計 {total} 件',
    page: '{current} ページ目',
    pageSize: '1ページあたり {size} 件',
    goto: 'ジャンプ',
    prev: '前のページ',
    next: '次のページ',
    first: '最初のページ',
    last: '最後のページ'
  },

  // 時間関連
  time: {
    now: 'たった今',
    minutesAgo: '{minutes} 分前',
    hoursAgo: '{hours} 時間前',
    daysAgo: '{days} 日前',
    weeksAgo: '{weeks} 週間前',
    monthsAgo: '{months} ヶ月前',
    yearsAgo: '{years} 年前',
    today: '今日',
    yesterday: '昨日',
    tomorrow: '明日',
    thisWeek: '今週',
    lastWeek: '先週',
    thisMonth: '今月',
    lastMonth: '先月',
    thisYear: '今年',
    lastYear: '昨年'
  },

  // ステータス
  status: {
    active: 'アクティブ',
    inactive: '非アクティブ',
    enabled: '有効',
    disabled: '無効',
    online: 'オンライン',
    offline: 'オフライン',
    pending: '保留中',
    processing: '処理中',
    completed: '完了',
    failed: '失敗',
    cancelled: 'キャンセル',
    expired: '期限切れ',
    valid: '有効',
    invalid: '無効'
  }
}
