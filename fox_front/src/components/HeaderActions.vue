<template>
  <div class="header-actions">
    <div class="left-section">
      <div class="greeting-section" v-if="isLogined">
        <el-tag type="primary">
          <el-icon class="greeting-icon">
            <component :is="greetingIcon" />
          </el-icon>
          {{ greeting() }}，{{ userTypeTip }}{{ user.userToken }}
        </el-tag>
      </div>
      <div class="rate-section" v-if="isLogined">
        <el-tooltip placement="bottom" effect="dark">
          <template #content>
            <div class="rate-tooltip-table-wrapper">
              <el-table :data="rateLimits" border style="width: 100%; font-size: 16px; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 24px rgba(0,0,0,0.12);">
                <el-table-column prop="model" label="模型" width="100" align="center">
                  <template #default="scope">
                    <b>{{ scope.row.model }}</b>
                  </template>
                </el-table-column>
                <el-table-column label="可用/总速率" width="130" align="center">
                  <template #default="scope">
                    <span style="font-weight: 500;">{{ scope.row.availableToken }}/{{ scope.row.limit }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="周期" width="80" align="center">
                  <template #default="scope">
                    <span>{{ useLimitMap[scope.row.per] || scope.row.per }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
          <el-tag type="success">
            <template v-if="gpt4oRateLimit">
              gpt-4o 可用速率 {{ gpt4oRateLimit.availableToken }}/{{ gpt4oRateLimit.limit }} ({{ useLimitMap[gpt4oRateLimit.per] || gpt4oRateLimit.per }})
            </template>
            <template v-else>
              可用速率 --/--
            </template>
          </el-tag>
        </el-tooltip>
      </div>
      <div class="expiry-section" v-if="isLogined">
        <el-tag :type="formatExpiryDate == '免费用户' ? 'success' : 'warning'">会员过期时间: {{ formatExpiryDate }}</el-tag>
      </div>
    </div>
    <div class="right-section">
      <el-divider direction="vertical" />
      <el-switch v-model="isDark" :active-icon="Moon" :inactive-icon="Sunny" inline-prompt @change="customToggleDark" />
    </div>
  </div>
  <div class="website-info">
    <el-image :src="config.systemLogo" v-if="config.systemLogo" style="width: 40px; height: 40px;"
      class="website-logo" />
    <chatgptIcon v-else style="width: 40px; height: 40px;" />
    <span class="website-name">{{ config.systemName }}</span>
  </div>
</template>

<script setup>
import { ref, defineProps, computed, reactive, getCurrentInstance, onMounted } from 'vue'
import { useDark, useToggle } from '@vueuse/core'
import chatgptIcon from '@/assets/chatgpt.svg'
import {
  Sunrise,
  Sunny,
  Moon,
  MoonNight,
  Lock,
  SwitchButton
} from '@element-plus/icons-vue'
import useUserStore from '@/store/user'
import { useRouter } from 'vue-router'
import moment from 'moment'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserCenter from '@/components/draw/UserCenter.vue'
const router = useRouter()
const userStore = useUserStore()
const user = ref(userStore.user)
const isLogined = ref(userStore.isLoggedIn)
import websiteLogoUrl from '@/assets/chatgpt.svg'
import { isMobile } from '@/utils'
import { useNotificationStore } from '../store/notificationStore'
const notificationStore = useNotificationStore()
let passwordFormRef = ref(null)
import api from '@/axios'
const props = defineProps({
  config: {
    type: Object,
    required: true,
  },
})
const formatExpiryDate = ref('')
const userTypeTip = ref('')
if (isLogined.value) {
  const now = moment()
  const plusExpireTime = moment(user.value.plusExpireTime)
  const expireTime = moment(user.value.expireTime)
  if (plusExpireTime.isAfter(now)) {
    formatExpiryDate.value = plusExpireTime.format('YYYY年-MM月-DD日 HH:mm')
  } else if (expireTime.isAfter(now)) {
    formatExpiryDate.value = expireTime.format('YYYY年-MM月-DD日 HH:mm')
  } else {
    formatExpiryDate.value = '免费用户'
  }
  const userType = user.value.userType
  if (userType == 2) {
    userTypeTip.value = '尊贵的会员用户'
  } else if (userType == 3) {
    userTypeTip.value = '尊贵的高级会员用户'
  }
}


const greeting = () => {
  const hour = new Date().getHours()
  if (hour >= 0 && hour < 5) return '凌晨好'
  if (hour >= 5 && hour < 12) return '早上好'
  if (hour >= 12 && hour < 14) return '中午好'
  if (hour >= 14 && hour < 18) return '下午好'
  return '晚上好'
}

const greetingIcon = computed(() => {
  const hour = new Date().getHours()
  if (hour >= 0 && hour < 5) return MoonNight
  if (hour >= 5 && hour < 12) return Sunrise
  if (hour >= 12 && hour < 18) return Sunny
  return Moon
})

const passwordDialogVisible = ref(false)
// Logout function
const logout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    userStore.logout()
    ElMessage.info('已退出登录')
    isLogined.value = false
    // router.push("/login")
  })
}
const handleCommand = (command) => {
  switch (command) {
    case 'changePassword':
      passwordDialogVisible.value = true // 根据实际路由配置修改
      break
    case 'logout':
      logout()
      break
    case 'gotoBackend':
      gotoBackend()
      break
    case 'myWorks':
      gotoMyWorks()
      break
    case 'userCenter':
      gotoUserCenter()
      break
  }
}
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const passwordRules = {
  currentPassword: [{ required: true, message: '请输入当前密码', trigger: 'blur' },
  { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  newPassword: [{ required: true, message: '请输入新密码', trigger: 'blur' },
  { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else if (value === passwordForm.value.currentPassword) {
          callback(new Error('新密码不能与当前密码相同'))
        } else {
          callback()
        }
      }, trigger: 'blur'
    },
  ],
}
const useLimitMap = reactive({
  '1h': '1小时',
  '3h': '3小时',
  '1d': '每天',
  '1w': '每周'
})
// Password change function
const submitPasswordChange = (passwordFormRef) => {
  passwordFormRef.validate(async (valid) => {
    if (valid) {
      console.log('Changing password...', user.value.userToken)

      const res = await api.post(`/api/chatGptUser/updatePassword`,
        { username: user.value.userToken, oldPassword: passwordForm.value.currentPassword, newPassword: passwordForm.value.newPassword })
      console.log(res)
      if (res.status !== 200 || res.data.code != 0) {
        ElMessage.error(res.data.msg)
        return
      }
      // Add password change logic here
      ElMessage.success('密码修改成功,请重新登录')
      userStore.logout()
      router.push("/login")
      passwordDialogVisible.value = false
    } else {
      ElMessage.error('请检查输入')
    }
  })
}
const { proxy } = getCurrentInstance()
const showNotification = ref(false)
const notification = ref(null)
const getHeaderNotification = async () => {
  const res = await api.get('/api/notification/getLatest?typeList=2')
  if (res.status == 200 && res.data.data) {
    let notifications = res.data.data
    if (notifications.length) {
      //根据createdAt 排序，最新的放在前面
      let theLatest = proxy.$getLatestNotification(notifications)
      if (theLatest) {
        notification.value = theLatest
        if (notificationStore.isVisible(theLatest.updatedAt)) {
          showNotification.value = true
        }
      }
    }
  }
}
const tryToShowNotification = () => {
  if (notification.value) {
    showNotification.value = true
  } else {
    ElMessage.info('暂无通知')
  }
}
const handleRead = () => {
  showNotification.value = false
  notificationStore.markAsRead('headerNotification', notification.value.updatedAt)
}
const rateLimits = ref([])
const gpt4oRateLimit = computed(() => {
  // 支持 gpt-4o、gpt4o、gpt-4o-mini 等
  return rateLimits.value.find(item =>
    item.model && (item.model.toLowerCase() === 'gpt-4o' || item.model.toLowerCase() === 'gpt4o' || item.model.toLowerCase() === 'gpt-4o-mini')
  )
})

onMounted(async () => {
  if (!isMobile()) {
    notificationStore.initReadStatus('headerNotification')
    getHeaderNotification()
  }
  // 拉取限速信息
  if (isLogined.value) {
    try {
      const res = await api.get('/api/chatGptUser/getRateLimit')
      if (res.status === 200 && res.data.code === 0) {
        rateLimits.value = res.data.data || []
      }
    } catch (e) {
      // 忽略错误
    }
  }
})
const isDark = useDark()
const customToggleDark = (e) => {
  console.log(e);
  if (e) {
    //暗色主题
    const theme = 'dark'
    document.documentElement.setAttribute('class', theme)
    useToggle(e)
  } else {
    //亮色主题
    const theme = 'light'
    document.documentElement.setAttribute('class', theme)
  }
};
const gotoBackend = () => {
  router.push('/dashboard')
}
const gotoLogin = () => {
  ElMessageBox.confirm('确定要前往登录页面吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.push('/login')
  })
}
const gotoMyWorks = () => {
  router.push('/myWorks')
}
const userCenterRef = ref(null)
const gotoUserCenter = () => {
  userCenterRef.value.openDialog()
}
</script>

<style scoped>
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-radius: 10px;
  gap: 20px;
}

.left-section {
  display: flex;
  gap: 20px;
  align-items: center;
}

.right-section {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

:deep(.el-icon) {
  font-size: 25px;
}

.greeting-section,
.rate-section,
.expiry-section {
  display: flex;
  align-items: center;
}

.avatar-section {
  cursor: pointer;
}

.el-dropdown-item [class^="el-icon"] {
  margin-right: 8px;
  vertical-align: middle;
}

.greeting-icon {
  margin-right: 4px;
  vertical-align: middle;
}

.password-dialog .dialog-content {
  padding: 10px 0;
}

.password-dialog .el-dialog__body {
  padding: 20px 20px 10px 20px;
}

.website-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.website-left {
  flex: 1;
  text-align: center;
  padding: 20px;
  border-radius: 15px;
  background-color: var(--el-bg-color-container);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.website-logo {
  border-radius: 50%;
}

.website-name {
  font-size: 30px;
  font-weight: bold;
}

@media (min-width: 769px) {
  .website-info {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 10px;
    display: none;
  }

  .left-section {
    flex-direction: column;
    align-items: end;
    width: 100%;
  }

  .greeting-section,
  .rate-section,
  .expiry-section {
    width: 100%;
    justify-content: center;
  }

  .password-dialog .el-dialog__body {
    padding: 0;
  }

  .password-dialog {
    width: 100%;
    --el-dialog-width: 90%;
  }

  :deep(.el-dialog) {
    --el-dialog-width: 90%;
  }
}

.rate-tooltip-table-wrapper {
  min-width: 320px;
  max-width: 400px;
  padding: 18px 18px 10px 18px;
  background: #fff;
  border-radius: 14px;
}

:deep(.el-table th) {
  background: #f5f7fa;
  font-weight: bold;
  font-size: 16px;
}

:deep(.el-table td) {
  font-size: 16px;
  height: 38px;
  text-align: center;
}

:deep(.el-table) {
  border-radius: 12px;
  overflow: hidden;
}
</style>