<template>
  <div class="node-grid">
    <el-row :gutter="20" v-if="cards.length">
      <el-col :xs="12" :sm="8" :md="6" :lg="5" :xl="4" v-for="(card, index) in cards" :key="index">
        <div class="custom-card" :class="card.type + '-card'" @click="handleClick(card)">
          <div class="card-content">
            <!-- Card Header -->
            <div class="card-header">
              <div class="icon-type">
                <claude-color v-if="card.type == 'claude'" />
                <grok-color v-else-if="card.type == 'grok'" />
                <chatgpt v-else />
              </div>
            </div>

            <!-- Card Title and Status -->
            <div class="title-section">
              <div class="card-title">{{ card.title }}</div>
              <div class="free-tag" v-if="card.type === 'free'">免费</div>
              <div class="fouro-tag" v-if="card.type === 'fouro'">4o</div>
              <div class="plus-tag" v-if="card.type === 'plus'">Plus</div>
            </div>

            <!-- Status Indicator -->
            <div class="status-section" :class="getStatusClass(card)">
              <span class="status-text">{{ getStatusText(card) }}</span>
              <div class="progress-indicator">
                <div v-for="(heat, index) in card.heatList" 
                     :key="index" 
                     class="progress-bar" 
                     :class="heat">
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-empty v-if="!cards.length" description="暂无推荐车辆" style="margin-top: 50px;"> </el-empty>
  </div>
</template>

<script setup>
import { defineProps, watch } from 'vue'
import { ref } from 'vue'
import useUserStore from '@/store/user'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus';
import { useRouter } from 'vue-router'
import api from '@/axios'
import chatgpt from '@/assets/chatgptColorful.svg'
import claudeColor from '@/assets/claude-color.svg'
import grokColor from '@/assets/grok.svg'
import FingerprintJS from '@fingerprintjs/fingerprintjs'

// 初始化FingerprintJS
const fpPromise = FingerprintJS.load();

// 获取浏览器指纹
const getVisitorId = async () => {
  try {
    // 先尝试从localStorage获取缓存的指纹
    let fingerprint = localStorage.getItem('visitor_fingerprint');
    if (fingerprint) {
      return fingerprint;
    }

    // 如果没有缓存，则重新生成
    const fp = await fpPromise;
    const result = await fp.get();

    // 使用访客ID作为指纹
    fingerprint = result.visitorId;
    fingerprint = "visitorId_" + fingerprint
    // 缓存指纹到localStorage
    localStorage.setItem('visitor_fingerprint', fingerprint);

    return fingerprint;
  } catch (error) {
    console.error('获取浏览器指纹失败:', error);
    // 如果获取指纹失败，生成一个随机ID作为后备方案
    const fallbackId = 'visitorId_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
    localStorage.setItem('visitor_fingerprint', fallbackId);
    return fallbackId;
  }
}

const router = useRouter()
const userStore = useUserStore()
const user = ref(userStore.user)
const isLogined = userStore.isLoggedIn
const props = defineProps({
  nodes: {
    type: Array,
    required: true
  },
  config: {
    type: Object,
    required: true
  }
})
// const format = (percentage) => ""
const cards = ref([])
watch(() => props.nodes, (newVal, oldVal) => {
  cards.value = []
  for (let item of newVal) {
    console.log(item)
    cards.value.push({
      type: item.type,
      title: item.carID,
      status: item.clearsIn == undefined || item.clearsIn < 0 ? '推荐' : '将于' + item.clearsIn + '秒后恢复',
      isTeam: !!item.isTeam,
      clearsIn: item.clearsIn,
      heatList: caclHeat(item),
    })

  }
  // console.log(cards.value)
})

const caclHeat = (card) => {
  if (!card.count) {
    return ['low', 'low', 'low', 'low']
  }
  let statuses = ['low', 'low', 'low', 'low'];
  const value = Number(card.count);
  if (!value) {
    return statuses;
  }
  if (value > 35) {
    statuses = ['high', 'high', 'high', 'high'];
  } else if (value >= 31) {
    statuses = ['medium', 'high', 'high', 'high'];
  } else if (value >= 26) {
    statuses = ['medium', 'medium', 'high', 'high'];
  } else if (value >= 21) {
    statuses = ['medium', 'medium', 'medium', 'high'];
  } else if (value >= 16) {
    statuses = ['medium', 'medium', 'medium', 'medium'];
  } else if (value >= 11) {
    statuses = ['low', 'medium', 'medium', 'medium'];
  } else if (value >= 6) {
    statuses = ['low', 'low', 'medium', 'medium'];
  } else if (value >= 1) {
    statuses = ['low', 'low', 'low', 'medium'];
  }
  return statuses;
}

const selectClaudeCarId = async (node) => {
  let claudeUrl = props.config.claudeUrl
  let carId = node.title
  let type = node.type
  let res = await api.get('/api/chatGpt/car/selectClaudeCar?carId=' + carId + '&type=' + type)
  if (res.status == 200 && res.data.code == 0) {
    let loginUrl = res.data.data

    window.open(loginUrl)
  } else {
    ElMessage({
      type: 'error',
      message: '选车失败:' + res.data.msg,
    })
  }
}
const selectGrokCarId = async (node) => {
  let grokUrl = props.config.grokUrl
  let carId = node.title
  let type = node.type
  let res = await api.get('/api/chatGpt/car/selectGrokCar?carId=' + carId + '&type=' + type)
  if (res.status == 200 && res.data.code == 0) {
    let loginUrl = res.data.data
    window.open(loginUrl)
  } else {
    ElMessage({
      type: 'error',
      message: '选车失败:' + res.data.msg,
    })
}
}
const selectCarId = async (node) => {
  let carId = node.title
  let type = node.type
  if (type == 'fouro') {
    type = '4o'
  }
  const res = await api.get('/api/chatGpt/car/selectCar?carId=' + carId + '&type=' + type)
  if (res.data.code == 0) {
    let carId = res.data.data
    if (carId) {
      let config = {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
      }
      const formData = new FormData()
      formData.append('usertoken', user.value.userToken || '')
      formData.append('action', 'default')
      const res = await api.post('/auth/login?carid=' + carId, formData, config)
      if (res.status == 200) {
        window.location.href = '/'
      } else {
        console.log(res)
        ElMessage({
          type: 'error',
          message: '登录失败,登录状态码:' + res.status
        })
      }

    } else {
      ElMessage({
        type: 'error',
        message: '选车失败，请刷新重试',
      })
    }
  } else {
    ElMessage({
      type: 'error',
      message: '选车失败:' + res.data.msg,
    })
  }
}
const handleClick = async (node) => {
  // console.log(`Clicked node ${node.title}`)
  if (!isLogined) {
    // 根据配置决定是否显示游客访问按钮
    const messageBoxOptions = {
      confirmButtonText: '去登录',
      type: 'info',
      showClose: true,
      distinguishCancelAndClose: true
    }
    const enableVisitor = props.config.enableVisitor === 'true'
    let confirmText = '你需要登录后才能访问'
    if (enableVisitor) {
      messageBoxOptions.cancelButtonText = '游客访问'
      messageBoxOptions.cancelButtonClass = 'guest-button'
      confirmText = '请选择访问方式'
    }

    ElMessageBox.confirm(confirmText, '提示', messageBoxOptions)
      .then(() => {
        router.push("/login")
      })
      .catch((action) => {
        if (action === 'cancel' && enableVisitor) {
          handleGuestAccess(node)
        }
      })
  } else {
    let loadingInstance = ElLoading.service({
      lock: true,
      text: '正在选择车辆...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    try {
      if (node.type == 'claude') {
        await selectClaudeCarId(node)
      } else if (node.type == 'grok') {
        await selectGrokCarId(node)
      } else {
        await selectCarId(node)
      }
    } catch (e) {
      ElMessage.error('选车失败:' + e.message)
    } finally {
      loadingInstance.close()
      console.log('closed loading')
    }
  }
}

// 修改游客访问处理函数
const handleGuestAccess = async (node) => {
  let loadingInstance = ElLoading.service({
    lock: true,
    text: '正在以游客身份访问...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    const visitorId = await getVisitorId();

    if (node.type == 'claude') {
      // Claude游客访问逻辑
      let claudeUrl = props.config.claudeUrl
      let carId = node.title
      let type = node.type
      let res = await api.get(`/api/chatGpt/car/selectClaudeCar?carId=${carId}&type=${type}&visitorId=${visitorId}`)
      if (res.status == 200 && res.data.code == 0) {
        let loginUrl = res.data.data
        window.open(claudeUrl + loginUrl)
      } else {
        ElMessage({
          type: 'warning',
          message: '访问受限: ' + res.data.msg,
        })
      }
    } else {
      // 其他类型访问逻辑
      let carId = node.title
      let type = node.type
      if (type == 'fouro') {
        type = '4o'
      }
      const res = await api.get(`/api/chatGpt/car/selectCar?carId=${carId}&type=${type}&visitorId=${visitorId}`)
      if (res.data.code == 0) {
        let selectedCarId = res.data.data
        if (selectedCarId) {
          const formData = new FormData()
          formData.append('usertoken', visitorId)
          formData.append('action', 'default')
          const res = await api.post('/auth/login?carid=' + selectedCarId, formData)
          if (res.status == 200) {
            window.location.href = '/'
          } else {
            ElMessage({
              type: 'warning',
              message: '访问受限，请尝试登录',
            })
          }
        } else {
          ElMessage({
            type: 'warning',
            message: '选车失败，请刷新重试',
          })
        }
      } else {
        ElMessage({
          type: 'warning',
          message: '访问受限: ' + res.data.msg,
        })
      }
    }
  } catch (e) {
    ElMessage.error('访问失败: ' + e.message)
  } finally {
    loadingInstance.close()
  }
}

// Add these new helper functions
const getStatusClass = (card) => {
  const isHigh = card.heatList.every(heat => heat === 'high');
  return isHigh ? 'status-busy' : 'status-normal';
}

const getStatusText = (card) => {
  if (card.clearsIn === undefined || card.clearsIn < 0) {
    return '推荐';
  }
  return '将于' + card.clearsIn + '秒后恢复';
}
</script>

<style scoped>
.node-grid {
  margin-top: 20px;
  overflow-y: auto;
  height: 100%;
  overflow-x: hidden;
  --free-color: rgb(36, 212, 174);
  --car-busy-color: #f7b05b;
  --car-very-busy-color: #f56c6c;
  padding: 0 10px;
}

.custom-card {
  background: rgba(0, 0, 0, 0.04);
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 20px;
  cursor: pointer;
  min-width: 145px;
  max-width: 225px;
  height: 127px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  outline: 1px solid white;
  outline-offset: -1px;
  transition: all 0.3s ease;
}

.custom-card:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.icon-type {
  width: 21px;
  height: 21px;
}

.card-tags {
  display: flex;
  gap: 8px;
}

.team-tag, .type-tag {
  border: none;
  padding: 2px 8px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 10px 0;
}

.card-title {
  font-size: 16px;
  font-family: MiSans, sans-serif;
  font-weight: 380;
  color: black;
}

.free-tag, .fouro-tag, .plus-tag {
  font-size: 12px;
  font-family: MiSans, sans-serif;
  font-weight: 520;
}

.free-tag {
  color: #5898EA;
}

.fouro-tag {
  color: #FF9800;
}
.plus-tag {
  color: #9C27B0;
}

.status-section {
  background: white;
  border-radius: 20px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  gap: 8px;
  width: fit-content;
}

.status-text {
  font-size: 13px;
  font-family: MiSans, sans-serif;
  font-weight: 520;
}

.status-busy .status-text {
  color: #FC5E5E;
}

.progress-indicator {
  display: flex;
  gap: 1px;
  width: 48px;
  height: 4px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  border-radius: 30px;
  background-color: var(--free-color);
}

.progress-bar.medium {
  background-color: var(--car-busy-color);
}

.progress-bar.high {
  background-color: var(--car-very-busy-color);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .custom-card {
    background: rgba(255, 255, 255, 0.08);
  }

  .card-title {
    color: white;
  }

  .status-section {
    background: rgba(255, 255, 255, 0.1);
  }
}

.claude-tag-2 { background-color: var(--claude-color); }
.grok-tag-2 { background-color: var(--grok-color); }
.free-tag-2 { background-color: var(--free-color); }
.fouro-tag-2 { background-color: var(--fouro-color); }
.plus-tag-2 { background-color: var(--plus-color); }
</style>