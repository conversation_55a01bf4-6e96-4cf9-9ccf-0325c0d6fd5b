<template>
    <div class="layout" :class="{ 'mobile-menu': isMobileView, 'menu-visible': isMenuVisible }">
        <!-- Website Information Section (1/3 of the layout) -->
        <div class="website-info">
            <el-image :src="config.systemLogo" v-if="config.systemLogo" style="width: 40px; height: 40px;"
                class="website-logo" />
            <chatgptIcon v-else style="width: 40px; height: 40px;margin-bottom: 10px;" />
            <span class="website-name">{{ config.systemName }}</span>
        </div>

        <!-- Menu Section -->
        <div class="layout-mid">
            <el-menu :router="true" :default-active="$route.path" class="custom-menu">
                <el-menu-item index="/carPage" @click="handleMenuClick">
                    <chatgptIcon class="icon" />
                    <span>ChatGpt</span>
                </el-menu-item>
                <el-menu-item index="/claudeCarPage" v-if="enableClaude === 'true'" @click="handleMenuClick">
                    <claudeIcon class="icon" />
                    <span>Claude</span>
                </el-menu-item>
                <el-menu-item index="/grokCarPage" v-if="enableGrok === 'true'" @click="handleMenuClick">
                    <grokIcon class="icon" />
                    <span>Grok</span>
                </el-menu-item>
                <el-menu-item index="/paint" v-if="enableDraw === 'true'" @click="handleMenuClick">
                    <drawSvg class="icon" />
                    <span>4O绘图</span>
                </el-menu-item>
                <el-divider class="menu-divider" />
                <el-menu-item index="/purchase" @click="handleMenuClick">
                    <purchaseIcon class="icon" />
                    <span>购买会员</span>
                </el-menu-item>
                <el-menu-item @click="openExchangeDialog">
                    <exchangeIcon class="icon" />
                    <span>兑换权益</span>
                </el-menu-item>
                <el-menu-item @click="selectOption('使用说明')">
                    <noteIcon class="icon" />
                    <span>使用说明</span>
                </el-menu-item>
                <el-menu-item @click="tryToShowNotification">
                    <noticeIcon class="icon" />
                    <span>站内通知</span>
                </el-menu-item>
            </el-menu>
        </div>

        <!-- Spacer to push user-info to bottom -->
        <div class="flex-spacer"></div>

        <!-- User Information Section -->
        <div class="user-info">
            <div class="avatar-section">
                <el-avatar :size="40" :src="user.avatar || avatar" v-if="isLogined" />
                <el-avatar :size="40" :src="avatar" v-else @click="gotoLogin" />
            </div>
            <div class="name-section" v-if="isLogined">
                <div class="user-name">
                    {{ user.userToken }}
                </div>
                <div class="expiry-section" v-if="isLogined">
                    {{ formatExpiryDate }}过期
                </div>
            </div>
            <el-dropdown @command="handleCommand" trigger="click" v-if="isLogined">
                <div class="arrow-icon">
                    <el-icon><ArrowUp /></el-icon>
                </div>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item command="userCenter">
                            <el-icon>
                                <User />
                            </el-icon>
                            用户中心
                        </el-dropdown-item>
                        <el-dropdown-item command="myWorks">
                            <el-icon>
                                <drawIcon />
                            </el-icon>
                            我的作品
                        </el-dropdown-item>
                        <el-dropdown-item command="changePassword">
                            <el-icon>
                                <Lock />
                            </el-icon>
                            修改密码
                        </el-dropdown-item>
                        <el-dropdown-item command="logout" divided>
                            <el-icon>
                                <SwitchButton />
                            </el-icon>
                            退出登录
                        </el-dropdown-item>
                        <el-dropdown-item command="gotoBackend" v-if="user.userToken == 'admin'" divided>
                            <el-icon>
                                <Setting />
                            </el-icon>
                            管理后台
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div>
    </div>
    <!-- 移动端菜单按钮 -->
    <div class="mobile-menu-button" v-if="isMobileView" @click="toggleMobileMenu">
        <foxIcon/>
    </div>
    <!-- 移动端遮罩层 -->
    <div class="mobile-overlay" v-if="isMobileView && isMenuVisible" @click="closeMobileMenu"></div>
    <!-- 悬浮购物图标 -->
    <el-dropdown class="floating-cart" trigger="click">
        <el-icon @click="toggleOptions">
            <foxIcon/>
        </el-icon>
        <template #dropdown>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click="getHeaderNotification" divide>
                    <Notification class="icon" />
                    <span>站内公告</span>
                </el-dropdown-item>
                <el-dropdown-item @click="selectOption('ChatGpt车队')" divide>
                    <chatgptIcon class="icon" />
                    ChatGpt
                </el-dropdown-item>
                <el-dropdown-item @click="selectOption('Cluade车队')" v-if="enableClaude === 'true'">
                    <claudeIcon class="icon" />
                    Cluade
                </el-dropdown-item>
                <el-dropdown-item @click="selectOption('站内购买')"> <el-icon>
                        <purchaseIcon />
                    </el-icon>站内购买</el-dropdown-item>
                <el-dropdown-item @click="selectOption('站内兑换')"> <el-icon>
                        <exchangeIcon />
                    </el-icon>站内兑换</el-dropdown-item>
                <el-dropdown-item @click="selectOption('使用说明')">
                    <noteIcon class="icon" />
                    使用说明
                </el-dropdown-item>
                <el-dropdown-item @click="selectOption('切换主题')">
                    <el-icon>
                        <Moon class="icon" />
                    </el-icon>
                    切换主题</el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
    <el-dialog v-model="showNotification" title="站内通知">
        <div v-richText="notification">

        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleRead">
                    我知道了
                </el-button>
            </div>
        </template>
    </el-dialog>
    <!-- Password Change Dialog -->
    <el-dialog title="修改密码" v-model="passwordDialogVisible" width="50%" class="password-dialog">
        <div class="dialog-content">
            <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-position="right">
                <el-form-item label="当前密码" prop="currentPassword">
                    <el-input v-model="passwordForm.currentPassword" type="password" placeholder="请输入当前密码"></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                    <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码"></el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                    <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请确认新密码"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <el-button @click="passwordDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitPasswordChange(passwordFormRef)">确认</el-button>
        </template>
    </el-dialog>
    <UserCenter ref="userCenterRef" />

    <!-- 站内兑换悬浮窗 -->
    <el-dialog 
        v-model="exchangeDialogVisible" 
        :show-close="false"
        width="480px" 
        :before-close="handleExchangeDialogClose"
        class="exchange-dialog"
        :destroy-on-close="true"
        :append-to-body="true"
        :modal="true"
        :lock-scroll="true"
        align-center
    >
        <template #header>
            <span></span>
        </template>
        
        <div class="modern-exchange-container">
            <!-- 头部区域 -->
            <div class="exchange-header-modern">
                <div class="exchange-title">激活码兑换</div>
                <div class="exchange-input-row">
                    <div class="modern-input-wrapper">
                        <input 
                            v-model="activationCode"
                            placeholder="输入兑换码"
                            :maxlength="64"
                            @keyup.enter="handleRedeem"
                            class="modern-input"
                        />
                    </div>
                    <div 
                        class="modern-exchange-button"
                        :class="{ 'loading': exchangeLoading }"
                        @click="handleRedeem"
                    >
                        <div v-if="!exchangeLoading" class="button-text">立即兑换</div>
                        <div v-else class="button-text">兑换中...</div>
                    </div>
                </div>
            </div>
            
            <!-- 底部提示区域 -->
            <div class="exchange-tips-modern">
                <div class="tips-text">
                    激活码可能有时间限制，请尽快使用<br/>
                    每个激活码仅能使用一次<br/>
                    如遇到问题，请联系客服人员
                </div>
            </div>
        </div>
        
        <template #footer>
            <span></span>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, defineProps, onMounted, getCurrentInstance, computed, reactive, nextTick } from 'vue'
import { House, Document, Setting, User, ShoppingCart, Present, Notification, Lock, SwitchButton, ArrowUp, Key } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router';
import chatgptIcon from '@/assets/chatgpt.svg'
import claudeIcon from '@/assets/claude-color.svg'
import grokIcon from '@/assets/grok.svg'
import exchangeIcon from '@/assets/exchange.svg'
import drawSvg from '@/assets/draw.svg'
import drawIcon from '@/assets/draw.svg'
import purchaseIcon from '@/assets/purchase.svg'
import noteIcon from '@/assets/note.svg'
import inviteIcon from '@/assets/invite.svg'
import noticeIcon from '@/assets/notice.svg'
import foxIcon from '@/assets/fox.svg'
import api from '@/axios'
import { isMobile } from '@/utils'
import { useNotificationStore } from '../store/notificationStore'
import { useDark } from '@vueuse/core'
import useUserStore from '@/store/user'
import moment from 'moment'
import avatar from '@/assets/kid.png'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserCenter from '@/components/draw/UserCenter.vue'
import { Sunrise, Sunny, Moon, MoonNight } from '@element-plus/icons-vue'
import { useWindowSize } from '@vueuse/core'

const props = defineProps({
    config: {
        type: Object,
        required: true,
    },
})
const enableDraw = computed(() => props.config.enableDraw)
const enableClaude = computed(() => props.config.enableClaude)
const enableGrok = computed(() => props.config.enableGrok)
const notificationStore = useNotificationStore()
const router = useRouter()

const route = useRoute()

const active_menu = ref('')
const isDark = useDark()
const customToggleDark = (e) => {
    console.log(e);
    if (e) {
        //暗色主题
        const theme = 'dark'
        document.documentElement.setAttribute('class', theme)
    } else {
        //亮色主题
        const theme = 'light'
        document.documentElement.setAttribute('class', theme)
    }
};


const noteSite = props['config']['noteSite']
const showOptions = ref(false);

const toggleOptions = () => {
    showOptions.value = !showOptions.value;
};

const selectOption = (option) => {
    if (option == '切换主题') {
        customToggleDark(isDark)
    } else if (option == '站内购买') {
        router.push('/purchase')
    } else if (option == 'ChatGpt车队') {
        router.push('/carPage')
    } else if (option == 'Cluade车队') {
        router.push('/claudeCarPage')
    } else if (option == '使用说明') {
        if (noteSite) {
            let url = noteSite
            if (!/^https?:\/\//i.test(url)) {
                url = 'https://' + url;
            }
            window.open(url, '_blank')
            return
        } else {

            router.push('/useNote')
        }

    } else if (option == '站内公告') {
        getHeaderNotification()
    } else if (option == '站内兑换') {
        openExchangeDialog()
    }
    showOptions.value = false; // 选择后隐藏选项
};
const { proxy } = getCurrentInstance()
const showNotification = ref(false)
const notification = ref('')
const getHeaderNotification = async () => {
    const res = await api.get('/api/notification/getLatest?typeList=2')
    if (res.status == 200 && res.data.data) {
        let notifications = res.data.data
        if (notifications.length) {
            //根据createdAt 排序，最新的放在前面
            let theLatest = proxy.$getLatestNotification(notifications)
            if (theLatest) {
                notification.value = theLatest.content
                if (notificationStore.isVisible(theLatest.updatedAt)) {
                    showNotification.value = true
                }
            }
        }
    }
}
const handleRead = () => {
    showNotification.value = false
    notificationStore.markAsRead('headerNotification', notification.value.updatedAt)
}

const tryToShowNotification = () => {
    if (notification.value) {
        showNotification.value = true
    } else {
        ElMessage.info('暂无通知')
    }
}

// User related data
const userStore = useUserStore()
const user = ref(userStore.user)
const isLogined = ref(userStore.isLoggedIn)
const formatExpiryDate = ref('')
const userTypeTip = ref('')
const passwordDialogVisible = ref(false)
const passwordFormRef = ref(null)
const rateLimits = ref([])

const gpt4oRateLimit = computed(() => {
    return rateLimits.value.find(item =>
        item.model && (item.model.toLowerCase() === 'gpt-4o' || item.model.toLowerCase() === 'gpt4o' || item.model.toLowerCase() === 'gpt-4o-mini')
    )
})

const passwordForm = ref({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
})

const passwordRules = {
    currentPassword: [
        { required: true, message: '请输入当前密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
    ],
    newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
    ],
    confirmPassword: [
        { required: true, message: '请确认新密码', trigger: 'blur' },
        {
            validator: (rule, value, callback) => {
                if (value !== passwordForm.value.newPassword) {
                    callback(new Error('两次输入密码不一致'))
                } else if (value === passwordForm.value.currentPassword) {
                    callback(new Error('新密码不能与当前密码相同'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        },
    ],
}



// User actions
const logout = () => {
    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        userStore.logout()
        ElMessage.info('已退出登录')
        isLogined.value = false
    })
}

const handleCommand = (command) => {
    switch (command) {
        case 'changePassword':
            passwordDialogVisible.value = true
            break
        case 'logout':
            logout()
            break
        case 'gotoBackend':
            gotoBackend()
            break
        case 'myWorks':
            gotoMyWorks()
            break
        case 'userCenter':
            gotoUserCenter()
            break
    }
}

const submitPasswordChange = (passwordFormRef) => {
    passwordFormRef.validate(async (valid) => {
        if (valid) {
            const res = await api.post(`/api/chatGptUser/updatePassword`,
                { username: user.value.userToken, oldPassword: passwordForm.value.currentPassword, newPassword: passwordForm.value.newPassword })
            if (res.status !== 200 || res.data.code != 0) {
                ElMessage.error(res.data.msg)
                return
            }
            ElMessage.success('密码修改成功,请重新登录')
            userStore.logout()
            router.push("/login")
            passwordDialogVisible.value = false
        } else {
            ElMessage.error('请检查输入')
        }
    })
}

const gotoLogin = () => {
    ElMessageBox.confirm('确定要前往登录页面吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        router.push('/login')
    })
}

const gotoMyWorks = () => {
    router.push('/myWorks')
}

const userCenterRef = ref(null)
const gotoUserCenter = () => {
    userCenterRef.value.openDialog()
}

// Initialize user data
if (isLogined.value) {
    const now = moment()
    const plusExpireTime = moment(user.value.plusExpireTime)
    const expireTime = moment(user.value.expireTime)
    if (plusExpireTime.isAfter(now)) {
        formatExpiryDate.value = plusExpireTime.format('YY.MM.DD')
    } else if (expireTime.isAfter(now)) {
        formatExpiryDate.value = expireTime.format('YY.MM.DD')
    } else {
        formatExpiryDate.value = '免费用户'
    }
    const userType = user.value.userType
    if (userType == 2) {
        userTypeTip.value = '尊贵的会员用户'
    } else if (userType == 3) {
        userTypeTip.value = '尊贵的高级会员用户'
    }
}

onMounted(async () => {
    if (route.path == '/home') {

        active_menu.value = '/carPage'
        router.push('/carPage')
    }
    if (isMobile()) {
        notificationStore.initReadStatus('headerNotification')
        getHeaderNotification()
    }

    // 拉取限速信息
    if (isLogined.value) {
        try {
            const res = await api.get('/api/chatGptUser/getRateLimit')
            if (res.status === 200 && res.data.code === 0) {
                rateLimits.value = res.data.data || []
            }
        } catch (e) {
            // 忽略错误
        }
    }
})

const exchangeDialogVisible = ref(false)
const activationCode = ref('')
const exchangeLoading = ref(false)

const handleRedeem = async () => {
    if (!activationCode.value) {
        ElMessage.warning('请输入激活码')
        return
    }

    try {
        exchangeLoading.value = true
        // 使用与原始 exchange.vue 相同的 API 调用
        const res = await api.post('/api/activationCode/exchange?activationCode=' + activationCode.value)
        if (res.data.code === 0) {
            ElMessage.success('兑换成功！')
            activationCode.value = ''
            exchangeDialogVisible.value = false
        } else {
            console.log(res)
            ElMessage.error(res.data.msg)
        }
    } catch (error) {
        ElMessage.error('兑换失败，请检查激活码是否正确')
    } finally {
        exchangeLoading.value = false
    }
}

const handleExchangeDialogClose = () => {
    exchangeDialogVisible.value = false
    activationCode.value = ''
}

const openExchangeDialog = () => {
    exchangeDialogVisible.value = true
}

const gotoBackend = () => {
    router.push('/dashboard')
}

// Add new refs and computed properties
const { width } = useWindowSize()
const isMobileView = computed(() => width.value <= 768)
const isMenuVisible = ref(false)

// Add new methods
const toggleMobileMenu = () => {
    isMenuVisible.value = !isMenuVisible.value
    if (isMenuVisible.value) {
        document.body.style.overflow = 'hidden'
    } else {
        document.body.style.overflow = ''
    }
}

const closeMobileMenu = () => {
    isMenuVisible.value = false
    document.body.style.overflow = ''
}

const handleMenuClick = () => {
    if (isMobileView.value) {
        closeMobileMenu()
    }
}
</script>

<style scoped>
.layout {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100vh; /* Make layout take full height */
    padding-right: 10px;
}

.website-info {
    display: flex;
    padding: 16px;
    padding-left:20px;
    padding-right: 20px;
    align-items: self-start;
    /* gap: 12px; */
    align-self: stretch;
    justify-content: center;
}

.website-logo {
    margin-bottom: 10px;
    border-radius: 50%;
}

.website-name {
    color: #FFF;
    font-family: MiSans;
    font-size: 22px;
    font-style: normal;
    font-weight: 520;
    line-height: normal;
    letter-spacing: -1.1px;
}

.layout-mid {
    flex: 2;
    text-align: center;
    padding: 20px;
    padding-top: 0px;
    padding-right: 0px;
    border-radius: 15px;
    /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
}

.menu-divider {
    margin: 0px;
}

.custom-menu {
    border-radius: 8px;
    border: none;
    padding: 4px;
    /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
    color: var(--el-text-color-regular);
}

.custom-menu .el-menu-item span {
    color: #FFF;
    font-family: MiSans;
    font-size: 15px;
    font-style: normal;
    font-weight: 520;
    line-height: normal;
}

.el-menu {
    background-color: transparent;
    --el-menu-base-level-padding: 16px;
}

.custom-menu .el-menu-item,
.custom-menu .el-sub-menu__title {
    display: flex;
    padding: 16px;
    align-items: center;
    gap: 6px;
    align-self: stretch;
    border-radius: 16px;
}

.custom-menu .el-menu-item.is-active {
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.10);
    background: #FFF;
}

.custom-menu .el-menu-item.is-active span {
    color: #000 !important;
}

.custom-menu .el-menu-item:hover {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.10);
}

.custom-menu .el-menu-item:hover span {
    color: #000 !important;
}



/* 子菜单样式 */
.el-menu--popup {
    border-radius: 6px;
    padding: 6px;
    min-width: 150px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.el-menu--popup .el-menu-item {
    height: 48px;
    line-height: 48px;
    padding: 0 20px;
    border-radius: 4px;
}

/* 动画效果 */
.el-menu-item,
.el-sub-menu__title {
    position: relative;
    overflow: hidden;
}

.el-menu-item::after,
.el-sub-menu__title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    transition: all 0.3s ease;
}

.el-menu-item:hover::after,
.el-sub-menu__title:hover::after {
    width: 80%;
    left: 10%;
}

@keyframes menuFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.el-menu--popup {
    animation: menuFadeIn 0.2s ease-out;
}

@media (min-width: 769px) {
    .floating-cart {
        display: none;
    }
}

.floating-cart {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
}

.floating-cart .el-button {
    width: 50px;
    /* 根据需要调整按钮大小 */
    height: 50px;
}

@media (max-width: 768px) {
    .layout {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        max-width: 300px;
        height: 100vh;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 43.99%, rgba(255, 255, 255, 0.14) 55%, #FFF 100%), radial-gradient(232.08% 120.13% at 68.41% 100%, #E9EEF1 0%, #44BDFF 100%);
        z-index: 2000;
        transition: left 0.3s ease;
        display: flex;
        padding: 20px 10px;
        overflow-y: auto;
    }

    .layout.mobile-menu.menu-visible {
        left: 0;
    }

    .mobile-menu-button {
        position: fixed;
        bottom: 20px;
        left: 20px;
        z-index: 2001;
        cursor: pointer;
        transition: transform 0.3s ease;
    }

    .mobile-menu-button:hover {
        transform: scale(1.1);
    }

    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1999;
        backdrop-filter: blur(4px);
    }

    .website-info {
        padding: 16px 10px;
    }

    .layout-mid {
        padding: 10px;
    }

    .custom-menu {
        width: 100%;
    }

    .custom-menu .el-menu-item {
        padding: 12px;
    }

    .user-info {
        margin: 10px;
        padding: 12px;
    }

    .floating-cart {
        display: none;
    }
}

.icon {
    margin-right: 8px;
    vertical-align: middle;
    font-size: 18px;
    color: var(--el-text-color-regular);
}

.flex-spacer {
    flex-grow: 1;
}

.user-info {
    margin-top: auto;
    display: flex;
    padding: 16px;
    padding-top: 5px;
    padding-bottom: 5px;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.10);
    background: rgba(0, 0, 0, 0.05);
    justify-content: space-between;
    margin-left: 20px;
    margin-bottom: 20px;
}

.name-section,
.rate-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    flex: 1 0 0;
}
.user-name {
    color: #000;
font-family: MiSans;
font-size: 16px;
font-style: normal;
font-weight: 450;
line-height: normal;
}
.expiry-section{
    color: #000;
font-family: MiSans;
font-size: 12px;
font-style: normal;
font-weight: 450;
line-height: normal;
opacity: 0.3;
}

.avatar-section {
    cursor: pointer;
}

.greeting-icon {
    margin-right: 4px;
    vertical-align: middle;
}

.password-dialog .dialog-content {
    padding: 10px 0;
}

.password-dialog .el-dialog__body {
    padding: 20px 20px 10px 20px;
}

.rate-tooltip-table-wrapper {
    min-width: 320px;
    max-width: 400px;
    padding: 18px 18px 10px 18px;
    background: #fff;
    border-radius: 14px;
}

:deep(.el-table th) {
    background: #f5f7fa;
    font-weight: bold;
    font-size: 16px;
}

:deep(.el-table td) {
    font-size: 16px;
    height: 38px;
    text-align: center;
}

:deep(.el-table) {
    border-radius: 12px;
    overflow: hidden;
}

@media (max-width: 768px) {
    .password-dialog .el-dialog__body {
        padding: 0;
    }

    .password-dialog {
        width: 100%;
        --el-dialog-width: 90%;
    }

    :deep(.el-dialog) {
        --el-dialog-width: 90%;
    }
}

.arrow-icon {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.arrow-icon:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.arrow-icon .el-icon {
    font-size: 16px;
    color: var(--el-text-color-regular);
}

/* 现代化兑换弹窗样式 - 使用全局样式穿透 */
@media (max-width: 768px) {
    :global(.el-dialog.exchange-dialog) {
        --el-dialog-width: 90% !important;
    }
}

/* 使用全局选择器确保样式穿透 */
:global(.el-overlay .el-overlay-dialog .el-dialog.exchange-dialog) {
    background: transparent !important;
    border-radius: 16px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
}

:global(.el-overlay .el-overlay-dialog .el-dialog.exchange-dialog .el-dialog__header) {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
    height: 0 !important;
    min-height: 0 !important;
}

:global(.el-overlay .el-overlay-dialog .el-dialog.exchange-dialog .el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

:global(.el-overlay .el-overlay-dialog .el-dialog.exchange-dialog .el-dialog__footer) {
    padding: 0 !important;
    margin: 0 !important;
    border-top: none !important;
    height: 0 !important;
    min-height: 0 !important;
}

/* 备用方案：使用更高权重的全局选择器 */
:global(.el-dialog__wrapper .el-dialog.exchange-dialog) {
    background: transparent !important;
    border-radius: 16px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
}

:global(.el-dialog__wrapper .el-dialog.exchange-dialog .el-dialog__header) {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
    display: none !important;
}

:global(.el-dialog__wrapper .el-dialog.exchange-dialog .el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

:global(.el-dialog__wrapper .el-dialog.exchange-dialog .el-dialog__footer) {
    padding: 0 !important;
    margin: 0 !important;
    border-top: none !important;
    display: none !important;
}

/* 最强力的备用方案 */
:global(.el-dialog.exchange-dialog) {
    background: transparent !important;
    border-radius: 16px !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin-top: 0 !important;
    margin-left: 0 !important;
}

:global(.el-dialog.exchange-dialog .el-dialog__header) {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: none !important;
    display: none !important;
}

:global(.el-dialog.exchange-dialog .el-dialog__body) {
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

:global(.el-dialog.exchange-dialog .el-dialog__footer) {
    padding: 0 !important;
    margin: 0 !important;
    border-top: none !important;
    display: none !important;
}

.modern-exchange-container {
    width: 480px;
    background: white;
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 16px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex;
    margin: 0;
    position: relative;
}

.exchange-header-modern {
    align-self: stretch;
    padding: 24px;
    background: linear-gradient(180deg, #1EAFFF 0%, #C8B2FF 100%), #5CC4FD;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    display: flex;
}

.exchange-title {
    align-self: stretch;
    text-align: center;
    color: white;
    font-size: 24px;
    font-family: MiSans;
    font-weight: 450;
    line-height: 26.40px;
    word-wrap: break-word;
}

.exchange-input-row {
    align-self: stretch;
    height: 48px;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 12px;
    display: inline-flex;
}

.modern-input-wrapper {
    flex: 1 1 0;
    align-self: stretch;
    padding: 16px;
    background: rgba(246, 246, 246, 0.90);
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 12px;
    backdrop-filter: blur(40px);
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    display: flex;
}

.modern-input {
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    color: black;
    font-size: 15px;
    font-family: MiSans;
    font-weight: 330;
    word-wrap: break-word;
}

.modern-input::placeholder {
    opacity: 0.30;
    color: black;
    font-size: 15px;
    font-family: MiSans;
    font-weight: 330;
    word-wrap: break-word;
}

.modern-exchange-button {
    align-self: stretch;
    padding-left: 24px;
    padding-right: 24px;
    padding-top: 8px;
    padding-bottom: 8px;
    background: white;
    box-shadow: 0px 7px 22px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    display: flex;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modern-exchange-button:hover {
    transform: translateY(-2px);
    box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.3);
}

.modern-exchange-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.modern-exchange-button .button-text {
    color: #5CC4FD;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 520;
    line-height: 22.40px;
    word-wrap: break-word;
}

.exchange-tips-modern {
    align-self: stretch;
    padding: 24px;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    display: inline-flex;
}

.tips-text {
    opacity: 0.50;
    color: black;
    font-size: 16px;
    font-family: MiSans;
    font-weight: 380;
    line-height: 32px;
    word-wrap: break-word;
}

@media (max-width: 768px) {
    .modern-exchange-container {
        width: 90vw;
        max-width: 400px;
    }

    .exchange-header-modern {
        padding: 20px;
        gap: 12px;
    }

    .exchange-title {
        font-size: 20px;
        line-height: 22px;
    }

    .exchange-input-row {
        flex-direction: column;
        height: auto;
        gap: 8px;
    }

    .modern-exchange-button {
        padding: 12px 24px;
    }

    .exchange-tips-modern {
        padding: 20px;
    }

    .tips-text {
        font-size: 14px;
        line-height: 28px;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    .layout {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        max-width: 300px;
        height: 100vh;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 43.99%, rgba(255, 255, 255, 0.14) 55%, #FFF 100%), radial-gradient(232.08% 120.13% at 68.41% 100%, #E9EEF1 0%, #44BDFF 100%);
        z-index: 2000;
        transition: left 0.3s ease;
        display: flex;
        padding: 20px 10px;
        overflow-y: auto;
    }

    .layout.mobile-menu.menu-visible {
        left: 0;
    }

    .mobile-menu-button {
        position: fixed;
        bottom: 20px;
        left: 20px;
        z-index: 2001;
        cursor: pointer;
        transition: transform 0.3s ease;
    }

    .mobile-menu-button:hover {
        transform: scale(1.1);
    }

    .mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1999;
        backdrop-filter: blur(4px);
    }

    .website-info {
        padding: 16px 10px;
    }

    .layout-mid {
        padding: 10px;
    }

    .custom-menu {
        width: 100%;
    }

    .custom-menu .el-menu-item {
        padding: 12px;
    }

    .user-info {
        margin: 10px;
        padding: 12px;
    }

    .floating-cart {
        display: none;
    }
}

/* 优化移动端动画效果 */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.mobile-menu.menu-visible {
    animation: slideIn 0.3s ease forwards;
}

.mobile-overlay {
    animation: fadeIn 0.3s ease forwards;
}

/* 优化移动端触摸体验 */
@media (hover: none) {
    .custom-menu .el-menu-item:active {
        background: rgba(255, 255, 255, 0.9);
    }

    .mobile-menu-button:active {
        transform: scale(0.95);
    }
}
</style>