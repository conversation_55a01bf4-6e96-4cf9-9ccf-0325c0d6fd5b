<template>
  <!-- <div v-richText="content" v-if="content"></div> -->

  <div class="tab-container">
    <div class="greeting-container">
      <span class="greeting-text">{{ greeting() }}<br/></span>
      <span class="greeting-member">{{ userTypeTip }}{{ user.userToken }}</span>
    </div>
    <div class="tab-group">
      <div class="slider" :style="sliderStyle"></div>
      <input name="nav" type="radio" class="nav-radio" id="home" checked="checked" hidden />
      <div class="tab-button" ref="freeTab" :class="{ active: activeTab === 'free' }" @click="handleClick('free')">
          <free/>
        <span>{{ config.nodeFreeName || '免费节点' }}</span>
      </div>
      <div class="divider"></div>

      <input name="nav" type="radio" class="nav-radio" id="about" hidden />
      <div class="tab-button" ref="fourOTab" :class="{ active: activeTab === '4o' }" @click="handleClick('4o')">
        <fouro/>
        <span>{{ config.node4oName || '4o节点' }}</span>
      </div>

      <div class="divider"></div>

      <input name="nav" type="radio" class="nav-radio" id="contact" hidden />
      <div class="tab-button" ref="plusTab" :class="{ active: activeTab === 'plus' }" @click="handleClick('plus')">
        <plus/>
        <span>{{ config.nodePlusName || 'Plus节点' }}</span>
      </div>
    </div>

    <div class="tab-content">
      <div v-show="activeTab === 'free'" class="page-contents">
        <NodeGrid :nodes="freeNodes" :config="config" />
      </div>
      <div v-show="activeTab === '4o'" class="page-contents">
        <NodeGrid :nodes="fourONodes" :config="config" />
      </div>
      <div v-show="activeTab === 'plus'" class="page-contents">
        <NodeGrid :nodes="plusNodes" :config="config" />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps, getCurrentInstance, onMounted, onUnmounted, computed } from 'vue'
import NodeGrid from './NodeGrid.vue'
import api from '@/axios'
import free from '@/assets/node/free.svg'
import fouro from '@/assets/node/fouro.svg'
import plus from '@/assets/node/plus.svg'
import {
  Sunrise,
  Sunny,
  Moon,
  MoonNight,
} from '@element-plus/icons-vue'
import useUserStore from '@/store/user'

const userStore = useUserStore()
const user = ref(userStore.user)
const userTypeTip = computed(() => {
  const userType = user.value.userType
  if (userType == 2) {
    return '尊贵的会员用户'
  } else if (userType == 3) {
    return '尊贵的高级会员用户'
  }
  return ''
})

const greeting = () => {
  const hour = new Date().getHours()
  if (hour >= 0 && hour < 5) return '凌晨好'
  if (hour >= 5 && hour < 12) return '早上好'
  if (hour >= 12 && hour < 14) return '中午好'
  if (hour >= 14 && hour < 18) return '下午好'
  return '晚上好'
}


const props = defineProps({
  config: {
      type: Object,
      required: true,
  },

})
const { proxy } = getCurrentInstance()
const content = ref('')
const getCarNotification = async () => {
  const res = await api.get('/api/notification/getLatest?typeList=4')
  if (res.data.code === 0) {
      let arr = res.data.data
      const notification = proxy.$getLatestNotification(arr)
      if (notification) {
          content.value = notification.content
      }
  }
}
getCarNotification()

const activeTab = ref('free')
const freeNodes = ref([])
const fourONodes = ref([])
const plusNodes = ref([])

const freeTab = ref(null)
const fourOTab = ref(null)
const plusTab = ref(null)

const sliderStyle = ref({
  left: '0px',
  width: '0px'
})

const updateSliderPosition = () => {
  const tabs = {
    'free': freeTab.value,
    '4o': fourOTab.value,
    'plus': plusTab.value
  }
  const activeElement = tabs[activeTab.value]
  if (activeElement) {
    sliderStyle.value = {
      left: `${activeElement.offsetLeft}px`,
      width: `${activeElement.offsetWidth}px`
    }
  }
}

const mockData = {
  free: [
    {
      type: 'free',
      carID: 'ChatGPT-Free-01',
      clearsIn: -1,
      count: 5,
      isTeam: false
    },
    {
      type: 'free',
      carID: 'ChatGPT-Free-02',
      clearsIn: 120,
      count: 25,
      isTeam: true
    },
    {
      type: 'free',
      carID: 'Claude-Free-01',
      clearsIn: -1,
      count: 15,
      isTeam: false
    },
    {
      type: 'free',
      carID: 'Grok-Free-01',
      clearsIn: 300,
      count: 35,
      isTeam: true
    }
  ],
  '4o': [
    {
      type: 'fouro',
      carID: 'ChatGPT-4o-01',
      clearsIn: -1,
      count: 10,
      isTeam: true
    },
    {
      type: 'fouro',
      carID: 'ChatGPT-4o-02',
      clearsIn: 180,
      count: 30,
      isTeam: false
    },
    {
      type: 'fouro',
      carID: 'Claude-4o-01',
      clearsIn: -1,
      count: 20,
      isTeam: true
    }
  ],
  plus: [
    {
      type: 'plus',
      carID: 'ChatGPT-Plus-01',
      clearsIn: -1,
      count: 8,
      isTeam: false
    },
    {
      type: 'plus',
      carID: 'ChatGPT-Plus-02',
      clearsIn: 240,
      count: 28,
      isTeam: true
    },
    {
      type: 'plus',
      carID: 'Claude-Plus-01',
      clearsIn: -1,
      count: 18,
      isTeam: false
    },
    {
      type: 'plus',
      carID: 'Grok-Plus-01',
      clearsIn: 150,
      count: 38,
      isTeam: true
    }
  ]
}

const reqNode = async (name) => {
  try {
    const res = await api.get('/api/chatGpt/car/list?type=' + name)
    if (res.data.code === 0) {
      if (name == 'free') {
        res.data.data.forEach(element => {
          element.type = 'free'
        });
        freeNodes.value = res.data.data;
        // freeNodes.value = mockData.free;
      } else if (name == 'plus') {
        res.data.data.forEach(element => {
          element.type = 'plus'
        });
        plusNodes.value = res.data.data;
        // plusNodes.value = mockData.plus;
      } else if (name == '4o') {
        res.data.data.forEach(element => {
          element.type = 'fouro'
        });
        fourONodes.value = res.data.data;
        // fourONodes.value = mockData['4o'];
      }
    } else {
      // 当API返回错误时，使用模拟数据
      if (name === 'free') {
        freeNodes.value = mockData.free;
      } else if (name === 'plus') {
        plusNodes.value = mockData.plus;
      } else if (name === '4o') {
        fourONodes.value = mockData['4o'];
      }
      console.log(`Using mock data for ${name} nodes`);
    }
  } catch (error) {
    console.error('Error fetching nodes:', error);
    // 当API请求失败时，也使用模拟数据
    if (name === 'free') {
      freeNodes.value = mockData.free;
    } else if (name === 'plus') {
      plusNodes.value = mockData.plus;
    } else if (name === '4o') {
      fourONodes.value = mockData['4o'];
    }
    console.log(`Using mock data for ${name} nodes due to API error`);
  }
}
const handleClick = async (tab) => {
  activeTab.value = tab
  await reqNode(tab)
  setTimeout(updateSliderPosition, 0)
}

onMounted(() => {
  updateSliderPosition()
  window.addEventListener('resize', updateSliderPosition)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateSliderPosition)
})

reqNode('free')
</script>
<style scoped>
@import url(@/style/nodetabs.css);
.tab-group{
  width: fit-content;
  font-size: 14px;
}

.greeting-container {
  text-align: left;
}

.greeting-text {
  color: black;
  font-size: 28px;
  font-family: MiSans;
  font-weight: 520;
  line-height: 32px;
  word-wrap: break-word;
}

.greeting-member {
  color: #5AC4FD;
  font-size: 28px;
  font-family: MiSans;
  font-weight: 520;
  line-height: 32px;
  word-wrap: break-word;
}

.tab-button {
  padding: 8px 16px;
  font-size: 14px;
}

.tab-button :deep(svg) {
  width: 20px;
  height: 20px;
}
</style>