<!-- App.vue -->
<template>
  <el-container class="app-container">
    <!-- 侧边栏 -->
    <el-aside>
      <!-- <UserInfo v-if="!isAsideCollapsed" :config="config" /> -->
      <LeftMenu :config="config" />
    </el-aside>

    <el-container class="right-container">
      <!-- <el-header>
        <HeaderActions :config="config" />
      </el-header> -->
      <el-main>
        <el-backtop target=".el-main" :bottom="100" :right="100">
        </el-backtop>
        <!-- <NodeTabs :config="config" />
          -->
        <router-view :config="config" />
      </el-main>
    </el-container>

  </el-container>
</template>

<script>
import { ref, computed, reactive, onMounted } from 'vue'
import UserInfo from './UserInfo.vue'
import HeaderActions from './HeaderActions.vue'
import NodeTabs from './NodeTabs.vue'
import useUserStore from '../store/user'
import LeftMenu from './LeftMenu.vue'
import api from '@/axios'
import { useRouter } from 'vue-router'
import { useScriptLoader } from '@/useScriptLoader'

export default {
  name: 'App',
  components: {
    UserInfo,
    LeftMenu,
    HeaderActions,
    NodeTabs
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()
    const user = ref(userStore.user)
    // const isLogined = ref(userStore.isLoggedIn)
    const isLogined = userStore.isLoggedIn
    const config = ref({})
    // 控制侧边栏的展开/收起
    const isAsideCollapsed = ref(false)



    const getConfig = async () => {
      const res = await api.post('/api/config/get',
        ["systemName", "systemLogo", "siteNotice", "noteSite", "issuingCardSite", "claudeUrl",
          "nodeFreeName", "node4oName", "nodePlusName", "nodeClaudeName", "nodeGrokName", 'scripts', 'enableVisitor', 'enableDraw',
          "enableClaude", "enableGrok", "lyyClaudeUrl", "grokUrl"
        ])
      if (res.status !== 200) {
        ElMessage.error('获取配置失败')
        return
      }
      config.value = res.data.data
      console.log(config.value)
    }
    onMounted(async () => {
      await getConfig()
      if (config.value.scripts) {
        try {
          const { loadScriptTags } = useScriptLoader()
          await loadScriptTags(config.value.scripts)
        } catch (error) {
          console.error(error)
        }
      }
    })


    return {
      user,
      isLogined,
      isAsideCollapsed,
      config,
    }
  }
}
</script>


<style scoped>
.app-container {
  height: 100vh;
  overflow-y: auto;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.00) 43.99%, rgba(255, 255, 255, 0.14) 55%, #FFF 100%), radial-gradient(232.08% 120.13% at 68.41% 100%, #E9EEF1 0%, #44BDFF 100%);
  /* font-family: 'Arial, sans-serif'; */

}

.right-container {}

.el-aside {
  position: relative;
  width: inherit;
  width: 260px;
}

.collapse-btn {
  position: absolute;
  top: 10px;
  right: -20px;
  cursor: pointer;
  padding: 5px;
  border: 1px solid var(--el-border-color);
  border-radius: 50%;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.el-header {
  border-radius: 10px;
  height: auto;
  margin-bottom: 10px;
}

.el-main {
  padding: 20px;
  /* padding-top: 0px; */
  /* margin-top: 10px; */
  /* height: 100%; */
  overflow: auto;
  overflow-x: hidden;
  border-radius: 10px;
  border-top: 1px solid var(--el-border-color);
  border-radius: 16px;
background: #FFF;
margin: 20px;
}

.layout {
  display: flex;
  flex-direction: column;
  gap: 10px;
  /* padding: 10px; */
  padding-right: 10px;
}

.website-info {
  flex: 1;
  text-align: center;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.website-logo {
  margin-bottom: 10px;
  border-radius: 50%;
}

.website-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  /* margin-left: 20px; */
}

.user-info {
  flex: 2;
  text-align: center;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
@media (max-width: 768px) {
  .el-aside {
    width: 0px;
  }
}
</style>