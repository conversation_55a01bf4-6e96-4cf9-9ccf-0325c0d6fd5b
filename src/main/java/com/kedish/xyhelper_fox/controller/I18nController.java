package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.utils.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.LocaleResolver;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 国际化控制器
 */
@RestController
@Slf4j
@RequestMapping("/api/i18n")
public class I18nController {

    @Resource
    private LocaleResolver localeResolver;

    /**
     * 切换语言
     * @param lang 语言代码 (zh, en, ja)
     * @param request HttpServletRequest
     * @param response HttpServletResponse
     * @return 切换结果
     */
    @PostMapping("/changeLanguage")
    public FoxResult changeLanguage(@RequestParam String lang, 
                                   HttpServletRequest request, 
                                   HttpServletResponse response) {
        try {
            Locale locale;
            switch (lang.toLowerCase()) {
                case "zh":
                case "zh-cn":
                    locale = Locale.SIMPLIFIED_CHINESE;
                    break;
                case "en":
                case "en-us":
                    locale = Locale.ENGLISH;
                    break;
                case "ja":
                case "ja-jp":
                    locale = Locale.JAPANESE;
                    break;
                default:
                    locale = Locale.SIMPLIFIED_CHINESE; // 默认中文
            }
            
            localeResolver.setLocale(request, response, locale);
            log.info("语言切换成功: {}", locale);
            return FoxResult.ok("语言切换成功");
        } catch (Exception e) {
            log.error("语言切换失败", e);
            return FoxResult.fail("语言切换失败");
        }
    }

    /**
     * 获取当前语言
     * @return 当前语言信息
     */
    @GetMapping("/getCurrentLanguage")
    public FoxResult getCurrentLanguage() {
        Locale currentLocale = I18nUtils.getCurrentLocale();
        Map<String, Object> result = new HashMap<>();
        result.put("locale", currentLocale.toString());
        result.put("language", currentLocale.getLanguage());
        result.put("country", currentLocale.getCountry());
        result.put("displayName", currentLocale.getDisplayName());
        return FoxResult.ok(result);
    }

    /**
     * 获取支持的语言列表
     * @return 支持的语言列表
     */
    @GetMapping("/getSupportedLanguages")
    public FoxResult getSupportedLanguages() {
        Map<String, Object> languages = new HashMap<>();
        
        Map<String, String> zh = new HashMap<>();
        zh.put("code", "zh");
        zh.put("name", "中文");
        zh.put("nativeName", "中文");
        
        Map<String, String> en = new HashMap<>();
        en.put("code", "en");
        en.put("name", "English");
        en.put("nativeName", "English");
        
        Map<String, String> ja = new HashMap<>();
        ja.put("code", "ja");
        ja.put("name", "Japanese");
        ja.put("nativeName", "日本語");
        
        languages.put("zh", zh);
        languages.put("en", en);
        languages.put("ja", ja);
        
        return FoxResult.ok(languages);
    }

    /**
     * 获取指定语言的消息
     * @param key 消息键
     * @param lang 语言代码（可选）
     * @return 国际化消息
     */
    @GetMapping("/getMessage")
    public FoxResult getMessage(@RequestParam String key, 
                               @RequestParam(required = false) String lang) {
        try {
            String message;
            if (lang != null && !lang.isEmpty()) {
                Locale locale;
                switch (lang.toLowerCase()) {
                    case "zh":
                        locale = Locale.SIMPLIFIED_CHINESE;
                        break;
                    case "en":
                        locale = Locale.ENGLISH;
                        break;
                    case "ja":
                        locale = Locale.JAPANESE;
                        break;
                    default:
                        locale = I18nUtils.getCurrentLocale();
                }
                message = I18nUtils.getMessage(key, null, locale);
            } else {
                message = I18nUtils.getMessage(key);
            }
            
            Map<String, String> result = new HashMap<>();
            result.put("key", key);
            result.put("message", message);
            return FoxResult.ok(result);
        } catch (Exception e) {
            log.error("获取国际化消息失败", e);
            return FoxResult.fail("获取消息失败");
        }
    }
}
