package com.kedish.xyhelper_fox.controller;

import com.kedish.xyhelper_fox.model.resp.FoxResult;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.service.ChatGptConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/api/config")
public class ChatGptConfigController {

    @Resource
    private ChatGptConfigService chatGptConfigService;

    @PostMapping("/get")
    public FoxResult getConfigs(@RequestBody List<String> keys) {

        if (CollectionUtils.isEmpty(keys)) {
            return FoxResult.failWithI18n("common.param.error");
        }
        return FoxResult.ok(chatGptConfigService.getConfigMap(keys));

    }

    @PostMapping("/addOrUpdate")
    public FoxResult addOrUpdate(@RequestBody Map<String, String> configMaps) {
        ChatgptUser user = UserContext.getUser();
        if (!user.getIsAdmin()) {
            return FoxResult.failWithI18n("user.not.admin");
        }
        log.info("configMaps:{}", configMaps);
        chatGptConfigService.addOrUpdate(configMaps);
        return FoxResult.ok();
    }
}
