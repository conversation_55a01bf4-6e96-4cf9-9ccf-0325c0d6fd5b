package com.kedish.xyhelper_fox.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * 国际化工具类
 */
@Component
public class I18nUtils {

    private static MessageSource messageSource;

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        I18nUtils.messageSource = messageSource;
    }

    /**
     * 获取国际化消息
     * @param key 消息键
     * @return 国际化消息
     */
    public static String getMessage(String key) {
        return getMessage(key, null);
    }

    /**
     * 获取国际化消息
     * @param key 消息键
     * @param args 参数
     * @return 国际化消息
     */
    public static String getMessage(String key, Object[] args) {
        return getMessage(key, args, LocaleContextHolder.getLocale());
    }

    /**
     * 获取国际化消息
     * @param key 消息键
     * @param args 参数
     * @param locale 语言环境
     * @return 国际化消息
     */
    public static String getMessage(String key, Object[] args, Locale locale) {
        try {
            return messageSource.getMessage(key, args, locale);
        } catch (Exception e) {
            return key; // 如果找不到消息，返回键值
        }
    }

    /**
     * 获取当前语言环境
     * @return 当前语言环境
     */
    public static Locale getCurrentLocale() {
        return LocaleContextHolder.getLocale();
    }

    /**
     * 判断是否为中文环境
     * @return 是否为中文环境
     */
    public static boolean isChineseLocale() {
        Locale locale = getCurrentLocale();
        return Locale.SIMPLIFIED_CHINESE.equals(locale) || 
               Locale.TRADITIONAL_CHINESE.equals(locale) ||
               "zh".equals(locale.getLanguage());
    }

    /**
     * 判断是否为英文环境
     * @return 是否为英文环境
     */
    public static boolean isEnglishLocale() {
        Locale locale = getCurrentLocale();
        return Locale.ENGLISH.equals(locale) || 
               Locale.US.equals(locale) ||
               "en".equals(locale.getLanguage());
    }

    /**
     * 判断是否为日文环境
     * @return 是否为日文环境
     */
    public static boolean isJapaneseLocale() {
        Locale locale = getCurrentLocale();
        return Locale.JAPANESE.equals(locale) || 
               Locale.JAPAN.equals(locale) ||
               "ja".equals(locale.getLanguage());
    }
}
